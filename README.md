# Budget Tracker App

A comprehensive personal finance management app built with Flutter that helps users track their income and expenses while providing smart financial suggestions based on the 50/30/20 rule.

## 🎯 Features

### Core Features (MVP)

- **Income Management**: Add, edit, and delete income entries with different types (monthly, daily, one-time)
- **Expense Management**: Categorize expenses as needs, wants, or custom categories
- **Dashboard**: Real-time balance overview with income, expenses, and net balance
- **Smart Suggestions**: AI-powered financial advice based on the 50/30/20 rule
- **Reports & Analytics**: Visual charts showing spending patterns and trends
- **Transaction History**: View and manage all income and expense transactions

### Technical Features

- **SQLite Database**: Local data storage for offline functionality
- **Material 3 Design**: Modern, clean UI following Google's Material Design guidelines
- **Provider State Management**: Efficient state management for reactive UI updates
- **Charts & Visualizations**: Interactive pie charts and bar charts using fl_chart
- **Responsive Design**: Optimized for mobile devices

## 🏗️ Architecture

### Project Structure

```
lib/
├── main.dart                    # App entry point
├── models/                      # Data models
│   ├── income_model.dart       # Income data structure
│   ├── expense_model.dart      # Expense data structure
│   └── budget_summary.dart     # Budget calculations
├── screens/                     # UI screens
│   ├── dashboard_screen.dart   # Main dashboard
│   ├── add_income_screen.dart  # Add/edit income
│   ├── add_expense_screen.dart # Add/edit expenses
│   └── reports_screen.dart     # Charts and analytics
├── services/                    # Business logic
│   ├── db_service.dart         # SQLite database operations
│   ├── budget_provider.dart    # State management
│   └── suggestion_service.dart # 50/30/20 rule logic
└── widgets/                     # Reusable UI components
    ├── amount_input_field.dart # Currency input widget
    ├── date_picker_field.dart  # Date selection widget
    ├── balance_card.dart       # Balance display card
    ├── transaction_card.dart   # Transaction list item
    ├── suggestion_card.dart    # Financial advice card
    └── chart_widgets.dart      # Chart components
```

### Database Schema

- **income**: id, amount, source, date, type
- **expenses**: id, amount, category, date, type

## 🚀 Getting Started

### Prerequisites

- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Android Studio / VS Code
- Android device or emulator

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   flutter pub get
   ```
4. Run the app:
   ```bash
   flutter run
   ```

### Dependencies

- `provider`: State management
- `sqflite`: SQLite database
- `fl_chart`: Charts and visualizations
- `intl`: Internationalization and formatting
- `material_design_icons_flutter`: Additional icons

## 💡 Usage

### Adding Income

1. Tap the "Add Transaction" button
2. Select "Add Income"
3. Enter amount, source, date, and type
4. Save the transaction

### Adding Expenses

1. Tap the "Add Transaction" button
2. Select "Add Expense"
3. Enter amount, category, date, and type
4. Choose from suggested categories or create custom ones

### Viewing Reports

1. Navigate to the Reports tab
2. View spending breakdown with pie charts
3. Analyze trends with bar charts
4. Review monthly summaries

### Smart Suggestions

The app provides personalized financial advice based on:

- 50/30/20 rule compliance
- Spending patterns
- Balance analysis
- Category-specific recommendations

## 🎨 Design System

The app follows Material 3 design principles with:

- **Color Scheme**: Purple-based theme with semantic colors
- **Typography**: Clear hierarchy with appropriate font weights
- **Components**: Consistent card designs and input fields
- **Icons**: Material Design icons throughout the interface
- **Spacing**: Consistent padding and margins

## 🔮 Future Enhancements

- **Goal Setting**: Set and track financial goals
- **Bill Reminders**: Push notifications for upcoming bills
- **Data Export**: Export data to Excel/CSV
- **Budget Categories**: Custom budget categories and limits
- **Multi-currency Support**: Support for different currencies
- **Cloud Sync**: Backup and sync across devices
- **Dark Mode**: Theme switching capability

## 🛠️ Development

### Code Style

- Follows Flutter/Dart best practices
- Uses Provider for state management
- Implements proper error handling
- Includes comprehensive comments

### Testing

- Unit tests for business logic
- Widget tests for UI components
- Integration tests for user flows

## 📱 Screenshots

The app features:

- Clean, modern dashboard with balance overview
- Intuitive forms for adding transactions
- Beautiful charts showing spending patterns
- Smart suggestions for financial improvement
- Responsive design for all screen sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Material Design team for the design system
- fl_chart package for beautiful charts
- SQLite for reliable local storage

---

**Budget Tracker App** - Take control of your finances with smart insights and beautiful design! 💰📊
